package com.developer.faker.Service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Handler;
import android.view.View;
import com.developer.faker.Data.RecentIncomeInfo;
import com.developer.faker.Fragment.MainFragment;
import com.developer.faker.Utils.Const;
import com.developer.faker.Utils.Global;
import com.developer.faker.Utils.UtilBlock;
import com.developer.faker.Utils.UtilLogFile;
import com.developer.faker.Utils.Utils;
import java.io.IOException;
import java.util.Date;

/* loaded from: classes.dex */
public class NewPhonecallReceiver extends BroadcastReceiver {
    private static Date callStartTime;
    public static boolean isIncoming;
    private static int lastState;
    private static String savedNumber;
    private Context mContext = null;
    View view;

    public void onOutgoingCallStarted(Context context, String str, Date date) {
    }

    private void RefreshLog() {
        if (Global.fragment_State != Const.FRAGMENT_STATE_MAIN || MainFragment.m_instance == null) {
            return;
        }
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Service.NewPhonecallReceiver.1
            @Override // java.lang.Runnable
            public void run(){
                try {
                    MainFragment.m_instance.RefreshCallLog();
                } catch (Exception e) {
                    try {
                        UtilLogFile.getInstance(NewPhonecallReceiver.this.mContext).writeLog(e.toString());
                    } catch (IOException ioException) {
                        ioException.printStackTrace();
                    }
                }
            }
        }, 1000L);
    }

    private void createUI(Context context, String str, String str2) {
        Intent intent = new Intent(context, (Class<?>) FloatingViewService.class);
        Utils.getInstance();
        intent.putExtra("tel", Utils.getHypenPhoneNumber(str));
        intent.setAction(str2);
        if (Build.VERSION.SDK_INT >= 26) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
        if (str2 == "ACTION_FOREGROUND_STOP") {
            RefreshLog();
        }
    }

    public void onCallStateChanged(Context context, int i, String str) throws NoSuchMethodException, SecurityException {
        this.mContext = context;
        if (lastState == i || str == null || str.isEmpty()) {
            return;
        }
        String correctPhoneNumber = Utils.getCorrectPhoneNumber(str);
        if (i != 0) {
            if (i == 1) {
                int iIsNeedBlock = UtilBlock.getInstance(context).IsNeedBlock(correctPhoneNumber);
                if (iIsNeedBlock > 0) {
                    Utils.getInstance().rejectCall(context);
                    UtilBlock.getInstance(context).addBlockHistory(correctPhoneNumber, iIsNeedBlock, System.currentTimeMillis());
                    return;
                } else {
                    isIncoming = true;
                    callStartTime = new Date();
                    savedNumber = correctPhoneNumber;
                    onIncomingCallStarted(context, correctPhoneNumber, callStartTime);
                }
            } else if (i == 2 && lastState != 1) {
                isIncoming = false;
                callStartTime = new Date();
                onOutgoingCallStarted(context, savedNumber, callStartTime);
            }
        } else if (lastState == 1) {
            onMissedCall(context, savedNumber, callStartTime);
        } else if (isIncoming) {
            onIncomingCallEnded(context, savedNumber, callStartTime, new Date());
        } else {
            onOutgoingCallEnded(context, savedNumber, callStartTime, new Date());
        }
        lastState = i;
    }

    protected void onIncomingCallEnded(Context context, String str, Date date, Date date2) {
        createUI(context, str, "ACTION_FOREGROUND_STOP");
        SendCallResponse();
    }

    protected void onIncomingCallStarted(Context context, String str, Date date) {
        createUI(context, str, "ACTION_SHOW_NUMBER");
    }

    protected void onMissedCall(Context context, String str, Date date) {
        createUI(context, str, "ACTION_FOREGROUND_STOP");
        SendCallResponse();
    }

    public void onOutgoingCallEnded(Context context, String str, Date date, Date date2) {
        SendCallResponse();
    }

        /* JADX WARN: Removed duplicated region for block: B:12:0x0051  */
    @Override // android.content.BroadcastReceiver
    /*
        Code decompiled incorrectly, please refer to instructions dump.
    */
    public void onReceive(Context context, Intent intent) {
        int i;
        try {
            if (UtilAuth.getInstance(context).isHaveToken().booleanValue()) {
                if (intent.getAction().equals("android.intent.action.NEW_OUTGOING_CALL")) {
                    savedNumber = intent.getExtras().getString("android.intent.extra.PHONE_NUMBER");
                } else if (intent.getAction().equals("android.intent.action.PHONE_STATE")) {
                    String string = intent.getExtras().getString("state");
                    String string2 = intent.getExtras().getString("incoming_number");
                    if (!string.equals(TelephonyManager.EXTRA_STATE_IDLE)) {
                        if (string.equals(TelephonyManager.EXTRA_STATE_OFFHOOK)) {
                            i = 2;
                            if (string2 != null && !string2.isEmpty()) {
                                if (UtilSetting.getInstance(context).POPUP_REMAIN) {
                                    createUI(context, string2, BuildConfig.FLAVOR);
                                } else {
                                    createUI(context, string2, "ACTION_FOREGROUND_STOP");
                                }
                            }
                        } else {
                            i = string.equals(TelephonyManager.EXTRA_STATE_RINGING) ? 1 : 0;
                        }
                        onCallStateChanged(context, i, string2);
                    }
                }
                if ("android.provider.Telephony.SMS_RECEIVED".equals(intent.getAction())) {
                    Object[] objArr = (Object[]) intent.getExtras().get("pdus");
                    if (objArr.length > 0) {
                        SmsMessage[] smsMessageArr = new SmsMessage[objArr.length];
                        for (int i2 = 0; i2 < objArr.length; i2++) {
                            smsMessageArr[i2] = SmsMessage.createFromPdu((byte[]) objArr[i2]);
                        }
                        Utils.getInstance().SendCallResult(context, smsMessageArr[0].getOriginatingAddress(), 4);
                        RefreshLog();
                    }
                }
            }
        } catch (Exception unused) {
        }
    }

    private void SendCallResponse() {
        new Handler().postDelayed(new Runnable() { // from class: com.developer.faker.Service.NewPhonecallReceiver.2
            @Override // java.lang.Runnable
            public void run(){
                RecentIncomeInfo latestCall = Utils.getInstance().getLatestCall(NewPhonecallReceiver.this.mContext);
                if (latestCall == null || latestCall.phoneNumber == null) {
                    return;
                }
                String str = latestCall.phoneNumber;
                NewPhonecallReceiver newPhonecallReceiver = NewPhonecallReceiver.this;
                if (str.equals(NewPhonecallReceiver.savedNumber)) {
                    int i = 0;
                    if (latestCall.callTypeDetail == 5) {
                        i = 1;
                    } else if (latestCall.callTypeDetail == 1) {
                        i = 2;
                    } else if (latestCall.callTypeDetail == 3) {
                        i = 3;
                    }
                    Utils utils = Utils.getInstance();
                    Context context = NewPhonecallReceiver.this.mContext;
                    NewPhonecallReceiver newPhonecallReceiver2 = NewPhonecallReceiver.this;
                    utils.SendCallResult(context, NewPhonecallReceiver.savedNumber, i);
                }
            }
        }, 1000L);
    }
}